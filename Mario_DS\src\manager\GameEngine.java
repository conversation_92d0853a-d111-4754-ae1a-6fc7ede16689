package manager;

import java.awt.Dimension;
import java.awt.Graphics2D;
import java.awt.Point;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JFrame;
import javax.swing.WindowConstants;

import manager.ScoreManager.DatabaseConfig;
import manager.ScoreManager.GameObserver;
import manager.ScoreManager.GameSubject;
import manager.ScoreManager.PostgreSQLScoreSaver;
import model.Map;
import model.hero.Mario;
import views.ImageLoader;
import views.StartScreenSelection;
import views.UIManager;

public class GameEngine extends GameSubject implements Runnable {
  public Dimension screenSize;
  List<GameObserver> observers = new ArrayList<>();
  private MapManager mapManager;
  private UIManager uiManager;
  private SoundManager soundManager;
  private GameStatus gameStatus;
  private boolean isRunning;
  private Camera camera;
  private ImageLoader imageLoader;
  private Thread thread;
  private StartScreenSelection startScreenSelection = StartScreenSelection.START_GAME;
  private int selectedMap = 0;
  // Multiplayer support
  private MultiplayerManager multiplayerManager;

  private GameEngine() {
    init();
  }

  private void init() {
    // Set default window size instead of fullscreen
    int defaultWidth = 1280;
    int defaultHeight = 720;
    this.screenSize = new Dimension(defaultWidth, defaultHeight);
    imageLoader = new ImageLoader();
    // InputManager inputManager = new InputManager(this);
    gameStatus = GameStatus.START_SCREEN;
    camera = new Camera();
    uiManager = new UIManager(this, defaultWidth, defaultHeight);
    soundManager = new SoundManager();
    mapManager = new MapManager();
    // Initialize multiplayer manager
    multiplayerManager = new MultiplayerManager(this);
    JFrame frame = new JFrame("Super Mario Bros.");
    frame.add(uiManager);
    // frame.addKeyListener(inputManager);
    // frame.addMouseListener(inputManager);
    frame.pack();
    frame.setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);
    frame.setResizable(true); // Enable resizing
    frame.setLocationRelativeTo(null);
    frame.setVisible(true);
    // Add component listener to handle resize events
    frame.addComponentListener(new java.awt.event.ComponentAdapter() {
      @Override
      public void componentResized(java.awt.event.ComponentEvent e) {
        // Update screen size when window is resized
        screenSize = frame.getContentPane().getSize();
        uiManager.updateSize(screenSize.width, screenSize.height);
      }
    });
    new KeyBindingManager(this, frame);
    start();
  }

  private synchronized void start() {
    if (isRunning) {
      return;
    }
    isRunning = true;
    thread = new Thread(this);
    thread.start();
  }

  private void reset() {
    resetCamera();
    setGameStatus(GameStatus.START_SCREEN);
  }

  public void resetCamera() {
    camera = new Camera();
    soundManager.restartBackground();
  }

  public Camera getCamera() {
    return camera;
  }

  public void selectMapViaMouse() {
    String path = uiManager.selectMapViaMouse(uiManager.getMousePosition());
    if (path != null) {
      createMap(path, 400);
    }
  }

  public void selectMapViaKeyboard() {
    String path = uiManager.selectMapViaKeyboard(selectedMap);
    if (path != null) {
      createMap(path, 400);
    }
  }

  public void changeSelectedMap(boolean up) {
    selectedMap = uiManager.changeSelectedMap(selectedMap, up);
  }

  public void setMap(Map map) {
    mapManager.setMap(map);
  }

  public void createMap(String path, double timeLimit) {
    boolean loaded = mapManager.createMap(imageLoader, path, timeLimit);
    if (loaded) {
      setGameStatus(GameStatus.RUNNING);
      soundManager.restartBackground();
    } else {
      setGameStatus(GameStatus.START_SCREEN);
    }
  }

  @Override
  public void run() {
    long lastTime = System.nanoTime();
    double amountOfTicks = 60.0;
    double ns = 1000000000 / amountOfTicks;
    double delta = 0;
    long timer = System.currentTimeMillis();
    while (isRunning && !thread.isInterrupted()) {
      long now = System.nanoTime();
      delta += (now - lastTime) / ns;
      lastTime = now;
      while (delta >= 1) {
        if (gameStatus == GameStatus.RUNNING)
          gameLoop();
        delta--;
      }
      render();
      if (gameStatus != GameStatus.RUNNING) {
        timer = System.currentTimeMillis();
      }
      if (System.currentTimeMillis() - timer > 1000) {
        timer += 1000;
        mapManager.updateTime();
      }
    }
  }

  private void render() {
    uiManager.repaint();
  }

  private void gameLoop() {
    // Only update enemy logic on server, clients get enemy state from server
    boolean isServer = !multiplayerManager.getCurrentMode().isNetworkMode()
        || (multiplayerManager.getCurrentMode() == GameMode.NETWORK_HOST);

    updateLocations(isServer);
    checkCollisions(isServer);
    updateCamera();
    // Update multiplayer manager
    multiplayerManager.update();
    if (isGameOver()) {
      setGameStatus(GameStatus.GAME_OVER);
      endGame();
    }
    int missionPassed = passMission();
    if (missionPassed > -1) {
      mapManager.acquirePoints(missionPassed, "mario");
      mapManager.acquirePoints(missionPassed, "mario2");
      setGameStatus(GameStatus.MISSION_PASSED);
      endGame();
    }
  }

  private void updateCamera() {
    Mario mario = mapManager.getMario("mario");
    Mario mario2 = mapManager.getMario("mario2");
    double marioX = mario.getX();
    double mario2X = mario2.getX();
    // Use enhanced camera system
    boolean isMultiplayer = multiplayerManager.getCurrentMode().isMultiplayer();
    double optimalCameraX = camera.calculateOptimalCameraPosition(marioX, mario2X, screenSize.width, isMultiplayer);
    double cameraX = camera.getX();
    double targetCameraX = optimalCameraX;
    // Smooth camera movement instead of instant snap
    double cameraDiff = targetCameraX - cameraX;
    double cameraSpeed = 0.1; // Adjust for smoother/faster camera movement
    camera.moveCam(cameraDiff * cameraSpeed);
    // Keep camera X bounded (prevent going left of start)
    camera.setX(Math.max(0, camera.getX()));
  }

  private void updateLocations() {
    updateLocations(true);
  }

  private void updateLocations(boolean updateEnemies) {
    mapManager.updateLocations(updateEnemies);
  }

  private void checkCollisions() {
    checkCollisions(true);
  }

  private void checkCollisions(boolean checkEnemies) {
    mapManager.checkCollisions(this, checkEnemies);
  }

  public void receiveInputMario(ButtonAction input) {
    if (gameStatus != GameStatus.RUNNING) {
      return;
    }

    Mario mario = mapManager.getMario("mario");
    if (input == ButtonAction.M_JUMP) {
      mario.jump(this);
    } else if (input == ButtonAction.M_RIGHT) {
      mario.move(true, camera);
    } else if (input == ButtonAction.M_LEFT) {
      mario.move(false, camera);
    } else if (input == ButtonAction.ACTION_COMPLETED) {
      mario.setVelX(0);
    } else if (input == ButtonAction.M_FIRE) {
      mapManager.fire(this, mario.getWhichMario());
    }
  }

  public void receiveInputMario2(ButtonAction input) {
    if (gameStatus != GameStatus.RUNNING) {
      return;
    }
    Mario mario = mapManager.getMario("mario2");
    if (input == ButtonAction.M2_JUMP) {
      mario.jump(this);
    } else if (input == ButtonAction.M2_RIGHT) {
      mario.move(true, camera);
    } else if (input == ButtonAction.M2_LEFT) {
      mario.move(false, camera);
    } else if (input == ButtonAction.ACTION_COMPLETED) {
      mario.setVelX(0);
    } else if (input == ButtonAction.M2_FIRE) {
      mapManager.fire(this, mario.getWhichMario());
    }
  }

  public void receiveInput(ButtonAction input) {
    if (gameStatus == GameStatus.START_SCREEN) {
      if (input == ButtonAction.SELECT && startScreenSelection == StartScreenSelection.START_GAME) {
        startGame();
      } else if (input == ButtonAction.SELECT && startScreenSelection == StartScreenSelection.MULTIPLAYER) {
        setGameStatus(GameStatus.MULTIPLAYER_MODE_SELECTION);
      } else if (input == ButtonAction.SELECT && startScreenSelection == StartScreenSelection.VIEW_ABOUT) {
        setGameStatus(GameStatus.ABOUT_SCREEN);
      } else if (input == ButtonAction.SELECT && startScreenSelection == StartScreenSelection.VIEW_HELP) {
        setGameStatus(GameStatus.HELP_SCREEN);
      } else if (input == ButtonAction.SELECT && startScreenSelection == StartScreenSelection.GAME_SCORE) {
        setGameStatus(GameStatus.GAME_SCORE);
      } else if (input == ButtonAction.GO_UP) {
        selectOption(true);
      } else if (input == ButtonAction.GO_DOWN) {
        selectOption(false);
      }
    } else if (gameStatus == GameStatus.MULTIPLAYER_MODE_SELECTION) {
      handleMultiplayerModeSelection(input);
    } else if (gameStatus == GameStatus.MAP_SELECTION) {
      if (input == ButtonAction.SELECT) {
        selectMapViaKeyboard();
      } else if (input == ButtonAction.GO_UP) {
        changeSelectedMap(true);
      } else if (input == ButtonAction.GO_DOWN) {
        changeSelectedMap(false);
      }
    } else if (gameStatus == GameStatus.RUNNING) {
      if (input == ButtonAction.PAUSE_RESUME) {
        pauseGame();
      }
    } else if (gameStatus == GameStatus.PAUSED) {
      if (input == ButtonAction.PAUSE_RESUME) {
        pauseGame();
      }
    } else if (gameStatus == GameStatus.GAME_OVER && input == ButtonAction.GO_TO_START_SCREEN) {
      reset();
    } else if (gameStatus == GameStatus.MISSION_PASSED && input == ButtonAction.GO_TO_START_SCREEN) {
      reset();
    }
    if (input == ButtonAction.GO_TO_START_SCREEN) {
      setGameStatus(GameStatus.START_SCREEN);
    }
  }

  private void selectOption(boolean selectUp) {
    startScreenSelection = startScreenSelection.select(selectUp);
  }

  private void handleMultiplayerModeSelection(ButtonAction input) {
    if (input == ButtonAction.SELECT) {
      int selectedOption = multiplayerManager.getSelectedMultiplayerOption();
      if (selectedOption == 0) { // Local Multiplayer
        multiplayerManager.setGameMode(GameMode.LOCAL_MULTIPLAYER);
        setGameStatus(GameStatus.MAP_SELECTION);
      } else if (selectedOption == 1) { // Network Host
        multiplayerManager.setGameMode(GameMode.NETWORK_HOST);
        multiplayerManager.startHosting(8080);
        setGameStatus(GameStatus.MAP_SELECTION);
      } else if (selectedOption == 2) { // Network Client
        multiplayerManager.setGameMode(GameMode.NETWORK_CLIENT);
        String host = javax.swing.JOptionPane.showInputDialog("Enter Host IP");
        if (host != null && !host.trim().isEmpty()) {
          boolean connected = multiplayerManager.connectToHost(host, 8080);
          if (connected) {
            setGameStatus(GameStatus.WAITING_FOR_SERVER);
          } else {
            javax.swing.JOptionPane.showMessageDialog(null, "Failed to connect to host.", "Connection Error",
                javax.swing.JOptionPane.ERROR_MESSAGE);
            setGameStatus(GameStatus.MULTIPLAYER_MODE_SELECTION);
          }
        }
      }
    } else if (input == ButtonAction.GO_UP) {
      multiplayerManager.changeSelectedMultiplayerOption(true);
    } else if (input == ButtonAction.GO_DOWN) {
      multiplayerManager.changeSelectedMultiplayerOption(false);
    } else if (input == ButtonAction.GO_TO_START_SCREEN) {
      setGameStatus(GameStatus.START_SCREEN);
    }
  }

  private void startGame() {
    // Set single player mode for regular start game
    multiplayerManager.setGameMode(GameMode.LOCAL_SINGLE_PLAYER);
    if (gameStatus != GameStatus.GAME_OVER) {
      setGameStatus(GameStatus.MAP_SELECTION);
    }
  }

  private void pauseGame() {
    if (gameStatus == GameStatus.RUNNING) {
      setGameStatus(GameStatus.PAUSED);
      soundManager.pauseBackground();
    } else if (gameStatus == GameStatus.PAUSED) {
      setGameStatus(GameStatus.RUNNING);
      soundManager.resumeBackground();
    }
  }

  public void shakeCamera() {
    camera.shakeCamera();
  }

  private boolean isGameOver() {
    if (gameStatus == GameStatus.RUNNING) {
      return mapManager.isGameOver();
    }
    return false;
  }

  public ImageLoader getImageLoader() {
    return imageLoader;
  }

  public GameStatus getGameStatus() {
    return gameStatus;
  }

  public StartScreenSelection getStartScreenSelection() {
    return startScreenSelection;
  }

  public void setGameStatus(GameStatus gameStatus) {
    this.gameStatus = gameStatus;
  }

  public int getScore() {
    return mapManager.getScore("mario");
  }

  public int getScore2() {
    return mapManager.getScore("mario2");
  }

  public int getRemainingLives() {
    return mapManager.getRemainingLives("mario") + mapManager.getRemainingLives("mario2");
  }

  public int getCoins() {
    return mapManager.getCoins("mario") + mapManager.getCoins("mario2");
  }

  public int getSelectedMap() {
    return selectedMap;
  }

  public void drawMap(Graphics2D g2) {
    mapManager.drawMap(g2);
  }

  public Point getCameraLocation() {
    return new Point((int) camera.getX(), (int) camera.getY());
  }

  private int passMission() {
    return mapManager.passMission();
  }

  public void playCoin() {
    soundManager.playCoin();
  }

  public void playOneUp() {
    soundManager.playOneUp();
  }

  public void playSuperMushroom() {
    soundManager.playSuperMushroom();
  }

  public void playMarioDies() {
    soundManager.playMarioDies();
  }

  public void playJump() {
    soundManager.playJump();
  }

  public void playFireFlower() {
    soundManager.playFireFlower();
  }

  public void playFireball() {
    soundManager.playFireball();
  }

  public void playStomp() {
    soundManager.playStomp();
  }

  public MapManager getMapManager() {
    return mapManager;
  }

  public MultiplayerManager getMultiplayerManager() {
    return multiplayerManager;
  }

  public Mario getMario() {
    return mapManager.getMario("mario");
  }

  public Mario getMario2() {
    return mapManager.getMario("mario2");
  }

  // override lại các phương thức của class gamesubject
  public void attach(GameObserver observer) {
    observers.add(observer);
  }

  // Phương thức để hủy đăng ký một observer
  public void detach(GameObserver observer) {
    observers.remove(observer);
  }

  // Phương thức thông báo cho tất cả observers
  public void notifyObservers() {
    for (GameObserver observer : observers) {
      observer.update(this);
    }
  }

  private void endGame() {
    // Thông báo cho observers
    notifyObservers();
    uiManager.refreshScores();
  }

  public static void main(String... args) {
    GameEngine gameEngine = new GameEngine();
    DatabaseConfig.testConnection();
    // Đăng ký một observer để lưu điểm vào PostgreSQL
    PostgreSQLScoreSaver scoreSaver = new PostgreSQLScoreSaver();
    gameEngine.attach(scoreSaver);
  }

  public int getRemainingTime() {
    return mapManager.getRemainingTime();
  }
}
