package manager.network;

import manager.ButtonAction;
import manager.MultiplayerManager;
import manager.network.message.GameState;
import manager.network.message.HeartbeatMessage;
import manager.network.message.InputMessage;
import manager.network.tool.HeartbeatManager;
import manager.network.tool.LatencyManager;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * Manages network communication for multiplayer gaming. Handles both server
 * (host) and client connections.
 */
public class NetworkManager {

  private MultiplayerManager multiplayerManager;
  private boolean isServer;
  private boolean isConnected;

  private ServerSocket serverSocket;
  private Socket clientSocket;
  private ObjectOutputStream out;
  private ObjectInputStream in;
  private HeartbeatManager heartbeatManager;
  private LatencyManager latencyManager;
  private Thread serverThread;
  private final Object outputStreamLock = new Object();

  public NetworkManager(MultiplayerManager multiplayerManager) {
    this.multiplayerManager = multiplayerManager;
    this.isServer = false;
    this.isConnected = false;
    this.latencyManager = new LatencyManager();
  }

  /**
   * Start a server to host a multiplayer game
   *
   * @param port The port to listen on
   * @return true if server started successfully
   */
  public boolean startServer(int port) {
    try {
      serverSocket = new ServerSocket(port);
      System.out.println("Server started on port " + port);
      logServerIp();
      isServer = true;

      // Wait for a client to connect
      serverThread = new Thread(() -> {
        try {
          clientSocket = serverSocket.accept();
          System.out.println("Client connected: " + clientSocket.getInetAddress());
          out = new ObjectOutputStream(clientSocket.getOutputStream());
          in = new ObjectInputStream(clientSocket.getInputStream());
          isConnected = true;
          multiplayerManager.onPlayerConnected("player2");
          heartbeatManager = new HeartbeatManager(this);
          heartbeatManager.start();
          startReceiving();
        } catch (IOException e) {
          System.err.println("Error accepting client connection: " + e.getMessage());
          cleanup();
        }
      });
      serverThread.start();

      return true;
    } catch (IOException e) {
      System.err.println("Could not start server on port " + port + ": " + e.getMessage());
      return false;
    }
  }

  /**
   * Connect to a remote server
   *
   * @param hostIP The IP address of the host
   * @param port   The port to connect to
   * @return true if connection was successful
   */
  public boolean connectToServer(String hostIP, int port) {
    try {
      clientSocket = new Socket(hostIP, port);
      System.out.println("Connected to server at " + hostIP + ":" + port);
      out = new ObjectOutputStream(clientSocket.getOutputStream());
      in = new ObjectInputStream(clientSocket.getInputStream());
      isConnected = true;
      isServer = false;
      multiplayerManager.onPlayerConnected("player1");
      heartbeatManager = new HeartbeatManager(this);
      heartbeatManager.start();
      startReceiving();
      return true;
    } catch (IOException e) {
      System.err.println("Could not connect to server: " + e.getMessage());
      return false;
    }
  }

  /**
   * Send player input to the remote host
   *
   * @param action   The button action
   * @param playerId The player ID
   */
  public void sendPlayerInput(ButtonAction action, String playerId) {
    if (!isConnected)
      return;

    try {
      synchronized (outputStreamLock) {
        out.writeObject(new InputMessage(action, playerId));
        out.flush();
      }
    } catch (IOException e) {
      System.err.println("Error sending input: " + e.getMessage());
      cleanup();
    }
  }

  public void sendHeartbeat() {
    if (!isConnected)
      return;

    try {
      synchronized (outputStreamLock) {
        latencyManager.recordRequest();
        out.writeObject(new HeartbeatMessage());
        out.flush();
      }
    } catch (IOException e) {
      System.err.println("Error sending heartbeat: " + e.getMessage());
      cleanup();
    }
  }

  /**
   * Apply game state received from the network
   *
   * @param gameState The game state data
   */
  public void applyGameState(Object gameState) {
    if (gameState instanceof GameState) {
      multiplayerManager.onGameStateReceived((GameState) gameState);
    }
  }

  public void sendGameState(GameState gameState) {
    if (!isConnected || !isServer)
      return;

    try {
      synchronized (outputStreamLock) {
        out.writeObject(gameState);
        out.flush();
      }
    } catch (IOException e) {
      System.err.println("Error sending game state: " + e.getMessage());
      cleanup();
    }
  }

  /**
   * Check if currently connected to a network game
   *
   * @return true if connected
   */
  public boolean isConnected() {
    return isConnected;
  }

  /**
   * Check if this instance is acting as a server
   *
   * @return true if server mode
   */
  public boolean isServer() {
    return isServer;
  }

  public long getLatency() {
    return latencyManager.getLatency();
  }

  /**
   * Cleanup network resources
   */
  public void cleanup() {
    if (heartbeatManager != null) {
      heartbeatManager.stop();
      heartbeatManager = null;
    }
    if (serverThread != null) {
      serverThread.interrupt();
      serverThread = null;
    }
    try {
      if (out != null)
        out.close();
      if (in != null)
        in.close();
      if (clientSocket != null)
        clientSocket.close();
      if (serverSocket != null)
        serverSocket.close();
    } catch (IOException e) {
      System.err.println("Error during network cleanup: " + e.getMessage());
    }
    isConnected = false;
    // isServer = false;
    System.out.println("Network resources cleaned up.");
    if (multiplayerManager != null) {
      multiplayerManager.onDisconnection();
    }
  }

  private void logServerIp() {
    System.out.println("Host IP Addresses:");
    try {
      Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
      while (interfaces.hasMoreElements()) {
        NetworkInterface iface = interfaces.nextElement();
        if (iface.isLoopback() || !iface.isUp()) {
          continue;
        }

        Enumeration<InetAddress> addresses = iface.getInetAddresses();
        while (addresses.hasMoreElements()) {
          InetAddress addr = addresses.nextElement();
          if (addr.isSiteLocalAddress()) {
            System.out.println("- " + iface.getDisplayName() + ": " + addr.getHostAddress());
          }
        }
      }
    } catch (SocketException e) {
      System.err.println("Could not get local IP address: " + e.getMessage());
    }
  }

  private void startReceiving() {
    new Thread(() -> {
      while (isConnected) {
        try {
          Object msg = in.readObject();
          if (msg instanceof GameState) {
            applyGameState(msg);
          } else if (msg instanceof InputMessage) {
            multiplayerManager.onInputReceived((InputMessage) msg);
          } else if (msg instanceof HeartbeatMessage) {
            if (heartbeatManager != null) {
              heartbeatManager.onHeartbeatReceived();
            }
            latencyManager.recordResponse();
            System.out.println("RTT: " + latencyManager.getRtt() + "ms");
          }
        } catch (IOException | ClassNotFoundException e) {
          if (isConnected) {
            System.err.println("Lost connection: " + e.getMessage());
            isConnected = false;
            if (multiplayerManager != null) {
              multiplayerManager.onDisconnection();
            }
          }
        }
      }
    }).start();
  }
}
